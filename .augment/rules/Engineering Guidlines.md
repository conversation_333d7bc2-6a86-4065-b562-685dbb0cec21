---
type: "manual"
---

1. Always pull full context before suggesting code (files, logic, use cases).
2. Default to diagram-first or contract-first design for complex features.
3. Reuse existing components; only write new code when no better path exists.
4. Apply clean code, SOLID, and DRY in every suggestion.
5. Use branches + commits with clean scope: 1 commit = 1 responsibility.
6. Track architectural decisions in `docs/timeline.md`.